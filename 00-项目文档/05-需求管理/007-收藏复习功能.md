# 用户故事：智能收藏复习的无感体验

## 1. 用户画像声明 (User Persona Statement)

作为一名 已经使用SenseWord学习了一段时间，积累了一定数量的收藏单词，希望在日常学习过程中自然地复习和巩固这些词汇的学习者张静，

我想要 一个能够在我正常探索无限内容流的过程中，智能地穿插我的收藏单词进行复习，让复习成为学习流程的自然组成部分，而不需要我专门安排复习时间，

以便于 我能够在不打断学习节奏的情况下，自然地巩固已学词汇，保持对收藏单词的记忆新鲜度，实现学习与复习的无缝融合。

## 2. 用户角色详述 (User Persona Detail)

张静，27岁，英语培训机构老师，对学习方法和记忆技巧有深入的理解。她知道复习是学习过程中不可缺少的环节，但她不喜欢被打断学习流程去专门做复习。她在使用SenseWord的过程中收藏了很多有价值的单词，希望这些词汇能够在她日常学习过程中自然地重新出现，让复习变成学习的自然组成部分。她相信最好的复习是无感的、自然的，不需要刻意安排。

## 3. 用户目标 (User Goal)

张静希望能够在日常的学习探索过程中，自然地遇到之前收藏的词汇，通过这种无感的复习方式巩固记忆，同时保持学习流程的连贯性和自然性。

## 4. 场景与过程 (Scenario & Process)

### 初始场景
张静在周末的下午，像往常一样打开SenseWord进行日常的词汇探索。她已经收藏了20多个词汇，包括`ubiquitous`、`serendipity`、`ephemeral`等，这些词汇已经有一段时间没有复习了。

### 详细交互流程

1. **正常的学习开始**：
   - 张静打开SenseWord，开始她的日常词汇探索
   - 她正在学习一个新词汇`innovation`，享受着无限内容流的沉浸式体验
   - 她完全没有意识到系统正在后台分析她的学习行为和收藏词汇的复习需求

2. **智能复习的自然穿插**：
   - 当张静完成对`innovation`的学习，向上滑动寻找下一个词汇时
   - 系统智能地判断：她上周收藏的`ubiquitous`已经5天没有复习了，访问次数较少
   - 下一个出现的词汇是`ubiquitous`，以完全相同的锚点形式展示：
     - 单词：`ubiquitous`
     - 发音：`/juːˈbɪkwɪtəs/`
     - 核心释义：无处不在的，普遍存在的
   - 张静感到一种熟悉感，但并没有意识到这是系统安排的复习

3. **无感复习的自然体验**：
   - 张静开始向上滑动，重新体验`ubiquitous`的各个维度解析
   - "意图"卡片：母语者使用这个词时想强调某事物的普遍性和无所不在
   - "情绪"卡片：带有一种"无法逃避"或"全面覆盖"的感觉
   - "想象"卡片：像空气一样无处不在，像网络信号一样覆盖每个角落
   - 她发现重新阅读这些内容时，理解更加深刻了，但这感觉就像是自然的学习流程

4. **收藏标记的温和提示**：
   - 在每张卡片的右上角，她看到了一个金色的星标，提醒她这是收藏的内容
   - 这个视觉标记让她意识到："哦，这是我之前收藏的词汇，正好复习一下"
   - 这种发现让她感到愉悦，而不是被打断的感觉

5. **继续自然的学习流程**：
   - 当她完成对`ubiquitous`的复习后，向上滑动进入下一个词汇
   - 系统继续提供相关的新词汇`pervasive`，保持学习的连贯性
   - 她完全没有意识到刚才经历了一次"复习"，感觉就像是正常的学习流程

6. **智能算法的后台工作**：
   - 系统在后台记录：`ubiquitous`刚刚被复习，更新其"上次曝光时间"
   - 算法调整：该词汇的复习优先级降低，下次出现时间延后
   - 同时，系统识别出另一个需要复习的收藏词汇`serendipity`（上次曝光7天前）

7. **持续的智能穿插**：
   - 在接下来的学习过程中，张静又自然地遇到了`serendipity`
   - 她再次感到熟悉和愉悦："又是一个我收藏的词汇！"
   - 这种偶遇式的复习让她感觉很自然，没有被强制复习的感觉

8. **无感复习的累积效果**：
   - 经过一个小时的学习，张静在不知不觉中复习了3个收藏词汇
   - 她完全没有感觉到被打断，学习流程始终保持自然和流畅
   - 系统的智能算法确保了她的收藏词汇得到适当的复习，同时不影响新知识的探索

## 5. 用户价值体现 (User Value Realization)

### 无感复习的自然体验
张静感叹道："我甚至没有意识到自己在复习！那些收藏的词汇会在我学习过程中自然地出现，就像老朋友的偶遇一样。这种复习方式完全不会打断我的学习节奏。"

### 学习流程的连贯性
"最棒的是，我不需要专门安排复习时间，也不需要切换到特殊的复习模式。复习就融合在我的日常学习中，保持了学习的连贯性和自然性。"

### 记忆巩固的有效性
"虽然复习是无感的，但效果很好。那些收藏的词汇会在合适的时机重新出现，让我在不知不觉中巩固了记忆。这比强制性的复习更有效。"

### 学习负担的减轻
"我不再需要担心'什么时候复习'、'复习哪些词汇'这些问题。系统会智能地帮我安排，我只需要专注于学习本身就好了。"

### 智能算法的信任感
"我能感觉到系统在背后智能地工作，它知道我什么时候需要复习什么词汇。这种智能化让我对系统产生了信任感，我相信它会帮我管理好学习进度。"

## 6. 验收标准 (Acceptance Criteria)

### AC1: 智能复习算法的实现
- **鉴于** 用户在无限内容流中进行学习
- **当** 系统需要决定下一个展示的词汇时
- **那么** 应根据收藏词汇的上次曝光时间和访问次数进行智能判断
- **并且** 应在适当时机自动穿插收藏词汇进行复习
- **并且** 复习的穿插应保持学习流程的自然性

### AC2: 复习时机的智能判断
- **鉴于** 系统需要决定是否推送收藏词汇进行复习
- **当** 分析用户的学习状态时
- **那么** 应考虑词汇的上次曝光时间（如超过3-7天）
- **并且** 应考虑词汇的历史访问次数（访问次数少的优先级更高）
- **并且** 应确保复习不会过于频繁打断新内容的学习

### AC3: 收藏状态的视觉标识
- **鉴于** 用户遇到收藏词汇进行无感复习
- **当** 显示收藏词汇内容时
- **那么** 应在卡片上显示金色星标等收藏标识
- **并且** 用户应能直接取消收藏
- **并且** 标识应温和提示而不打断学习流程

### AC4: 复习数据的实时更新
- **鉴于** 用户完成对收藏词汇的学习
- **当** 用户浏览完该词汇的内容时
- **那么** 系统应更新该词汇的"上次曝光时间"
- **并且** 应增加该词汇的访问次数计数
- **并且** 应调整该词汇在复习算法中的优先级

### AC5: 复习频率的智能控制
- **鉴于** 系统需要平衡复习和新内容学习
- **当** 在内容流中安排词汇时
- **那么** 收藏词汇的复习频率应适中（如每10-15个新词汇穿插1个复习）
- **并且** 应避免连续推送多个收藏词汇
- **并且** 应确保用户主要时间仍用于探索新内容

### AC6: 复习效果的数据追踪
- **鉴于** 系统需要优化复习算法
- **当** 用户进行学习活动时
- **那么** 应记录收藏词汇的复习频率和效果
- **并且** 应分析用户对复习内容的参与度
- **并且** 应根据数据反馈调整复习算法参数
